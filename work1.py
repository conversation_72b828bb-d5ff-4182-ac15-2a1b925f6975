# encoding: utf-8
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 第一步：先把数据读进来看看长啥样
print("1:\n")
df = pd.read_excel('housing.xlsx')  # 读取房价数据文件
print(df.head(10))  # 打印前10行数据，看看数据结构


print("2:\n")
# 第二步：画散点图看看各个特征和房价的关系
columns = df.select_dtypes(include=[np.number]).columns.tolist()  # 找出所有数值型的列
columns.remove('AreaId')  # 把AreaId去掉，这个不是我们要分析的

target = 'medianHouseValue'  # 目标变量就是房价中位数
# 选几个看起来比较重要的特征来分析
feature_columns = ['housingMedianAge', 'totalRooms', 'totalBedrooms', 'population', 'households', 'medianIncome']

n_features = len(feature_columns)
# 创建一个2x3的子图，这样6个特征刚好能放下
fig, axes = plt.subplots(2, 3, figsize=(15, 10))
fig.suptitle('medianHouseValue vs other')  # 给整个图加个标题

axes = axes.flatten()  # 把2D的axes数组拍平，方便后面用索引访问

# 遍历每个特征，画散点图
for i, feature in enumerate(feature_columns):
    if i < len(axes):
        axes[i].scatter(df[feature], df[target], alpha=0.6)  # alpha让点透明一些，重叠时能看清楚
        axes[i].set_xlabel(feature)  # x轴标签
        axes[i].set_ylabel(target)   # y轴标签
        axes[i].set_title(f'{target},{feature}')  # 每个子图的标题
        axes[i].grid(True, alpha=0.3)  # 加个网格，看起来清楚点

# 如果子图比特征多，把多余的隐藏掉
for i in range(len(feature_columns), len(axes)):
    axes[i].set_visible(False)

plt.tight_layout()  # 自动调整子图间距，避免重叠
plt.savefig('plt.png')  # 保存图片
plt.show()  # 显示图片

print("3:\n")
# 第三步：算相关系数，看看哪些特征和房价关系最密切
matrix = df[columns].corr()  # 计算所有数值列之间的相关系数矩阵
correlations = matrix[target].drop(target)  # 提取房价这一列，去掉自己和自己的相关系数

print("相关系数:")
# 按相关系数从大到小排序，看看哪个特征和房价最相关
for feature, corr in correlations.sort_values(ascending=False).items():
    print(f"{feature}: {corr:.4f}")
